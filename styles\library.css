/* Library Page Styles */
.library-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  color: var(--text-color);
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

/* Custom scrollbar styles for Webkit browsers (Chrome, Safari, Edge) */
.library-container::-webkit-scrollbar {
  width: 6px;
}

.library-container::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

.library-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.library-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

.library-header {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-in-out;
}

.library-header h1 {
  font-size: 42px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.library-header p {
  font-size: 16px;
  color: var(--subtitle-color);
}

.library-content {
  width: 100%;
  animation: slideUp 0.8s ease-in-out;
}

.empty-library {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
}

.empty-library i {
  font-size: 80px;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.empty-library h2 {
  font-size: 24px;
  margin-bottom: 15px;
  color: var(--text-color);
}

.empty-library p {
  font-size: 16px;
  color: var(--subtitle-color);
  max-width: 500px;
  margin-bottom: 25px;
}

.empty-library .action-btn {
  background-color: var(--secondary-color);
  border: none;
  color: var(--text-color);
  padding: 12px 25px;
  border-radius: 30px;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--transition);
  text-decoration: none;
}

.empty-library .action-btn:hover {
  background-color: var(--primary-color);
  transform: scale(1.05);
}

.empty-library .action-btn i {
  font-size: 18px;
  margin-right: 8px;
}

.library-songs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.library-song-item {
  background-color: var(--hover-light);
  border-radius: var(--border-radius);
  padding: 15px;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  border: 1px solid transparent;
}

.library-song-item:hover {
  background-color: var(--hover-color);
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-light);
  border-color: var(--border-color);
}

.library-song-item img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: var(--border-radius-small);
  margin-bottom: 10px;
}

.library-song-item h5 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 5px;
}

.library-song-item .subtitle {
  font-size: 12px;
  color: var(--subtitle-color);
}

.library-song-item .song-actions {
  position: absolute;
  top: 15px;
  right: 15px;
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.library-song-item:hover .song-actions {
  opacity: 1;
}

.library-song-item .song-actions i {
  font-size: 20px;
  color: var(--text-color);
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(5px);
}

.library-song-item .song-actions i:hover {
  color: var(--primary-color);
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
}

/* Heart icon specific styling in library */
.library-song-item .song-actions .add-to-library.bi-heart-fill {
  color: var(--primary-color);
  background-color: rgba(54, 226, 236, 0.2);
}

.library-song-item .song-actions .add-to-library.in-library {
  opacity: 1;
}

.library-song-item .song-actions .remove-from-library:hover {
  color: #e74c3c;
}

.library-song-item .img_play {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
}

.library-song-item .img_play img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-bottom: 0;
  border-radius: var(--border-radius-small);
}

.library-song-item .img_play i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 40px;
  color: var(--primary-color);
  opacity: 0;
  transition: var(--transition);
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
}

.library-song-item .img_play:hover i {
  opacity: 1;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .library-header h1 {
    font-size: 32px;
  }

  .library-songs {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
}
/* Navigation highlighting for library page only */
body:not(.music-detail-page) header .song_side nav ul li:nth-child(2) {
  color: #fff;
}

/* Ensure library page navigation doesn't conflict with music detail page */
body:not(.music-detail-page) header .song_side nav ul li:nth-child(2).active {
  color: #fff !important;
}
