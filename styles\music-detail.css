/* Music Detail Page Styles */
.music-detail-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  color: var(--text-color);
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--hover-light);
}

/* Custom scrollbar styles for Webkit browsers (Chrome, Safari, Edge) */
.music-detail-container::-webkit-scrollbar {
  width: 6px;
}

.music-detail-container::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

.music-detail-container::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.music-detail-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}

.music-detail-header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumb-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px;
}

.back-button {
  margin-right: 0;
}

.back-button a {
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: var(--transition);
  padding: 8px 16px;
  border-radius: var(--border-radius-small);
  background: var(--hover-light);
}

.back-button a:hover {
  background: var(--hover-color);
  transform: translateX(-3px);
}

.back-button i {
  font-size: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--subtitle-color);
}

.breadcrumb-source {
  color: var(--primary-color);
  font-weight: 500;
}

.breadcrumb-current {
  color: var(--text-color);
}

.breadcrumb i {
  font-size: 12px;
  opacity: 0.7;
}

.music-detail-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

.music-detail-content {
  display: flex;
  gap: 40px;
  margin-bottom: 40px;
}

.music-cover {
  position: relative;
  min-width: 300px;
  height: 300px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.music-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.music-cover:hover img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: var(--transition);
}

.music-cover:hover .play-button {
  opacity: 1;
}

.play-button i {
  font-size: 60px;
  color: var(--primary-color);
  cursor: pointer;
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.5));
  transition: var(--transition);
}

.play-button i:hover {
  transform: scale(1.1);
}

.music-info {
  flex: 1;
}

.music-info h1 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.music-info h3 {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 25px;
  color: var(--subtitle-color);
}

.music-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

/* Heart button specific styling */
.music-actions .heart-btn {
  background: linear-gradient(135deg, var(--primary-color), #2bc4cd);
  border: none;
  transition: all 0.3s ease;
}

.music-actions .heart-btn:hover {
  background: linear-gradient(135deg, #2bc4cd, var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(54, 226, 236, 0.3);
}

.music-actions .heart-btn.in-library {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.music-actions .heart-btn.in-library:hover {
  background: linear-gradient(135deg, #c0392b, #e74c3c);
}

.music-actions .heart-btn i {
  margin-right: 8px;
  transition: all 0.3s ease;
}

.music-actions .heart-btn:hover i {
  transform: scale(1.1);
}

.music-description,
.music-lyrics {
  margin-bottom: 25px;
}

.music-description h4,
.music-lyrics h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.music-description p,
.music-lyrics p {
  color: var(--subtitle-color);
  line-height: 1.6;
}

.music-description p,
.music-lyrics p {
  font-size: 14px;
  line-height: 1.6;
  color: #b3b3b3;
}

.related-songs {
  margin-top: 40px;
}

.related-songs h3 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #fff;
}

.related-songs-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.related-song-item {
  background-color: rgba(105, 105, 170, 0.1);
  border-radius: 10px;
  padding: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.related-song-item:hover {
  background-color: rgba(105, 105, 170, 0.3);
  transform: translateY(-5px);
}

.related-song-item img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 5px;
  margin-bottom: 10px;
}

.related-song-item h5 {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  margin-bottom: 5px;
}

.related-song-item .subtitle {
  font-size: 12px;
  color: #b3b3b3;
}

/* Responsive styles */
@media (max-width: 992px) {
  .music-detail-content {
    flex-direction: column;
  }

  .music-cover {
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .related-songs-container {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 576px) {
  .music-actions {
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Contextual styling based on source page */
.music-detail-container.library-context .breadcrumb-source {
  color: #e74c3c;
}

.music-detail-container.library-context .back-button a {
  background: linear-gradient(
    135deg,
    rgba(231, 76, 60, 0.1),
    rgba(192, 57, 43, 0.1)
  );
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.music-detail-container.library-context .back-button a:hover {
  background: linear-gradient(
    135deg,
    rgba(231, 76, 60, 0.2),
    rgba(192, 57, 43, 0.2)
  );
  border-color: rgba(231, 76, 60, 0.4);
}

.music-detail-container.recently-played-context .breadcrumb-source {
  color: #f39c12;
}

.music-detail-container.recently-played-context .back-button a {
  background: linear-gradient(
    135deg,
    rgba(243, 156, 18, 0.1),
    rgba(230, 126, 34, 0.1)
  );
  border: 1px solid rgba(243, 156, 18, 0.2);
}

.music-detail-container.recently-played-context .back-button a:hover {
  background: linear-gradient(
    135deg,
    rgba(243, 156, 18, 0.2),
    rgba(230, 126, 34, 0.2)
  );
  border-color: rgba(243, 156, 18, 0.4);
}

.music-detail-container.discovery-context .breadcrumb-source {
  color: var(--primary-color);
}

.music-detail-container.search-context .breadcrumb-source {
  color: #9b59b6;
}

.music-detail-container.search-context .back-button a {
  background: linear-gradient(
    135deg,
    rgba(155, 89, 182, 0.1),
    rgba(142, 68, 173, 0.1)
  );
  border: 1px solid rgba(155, 89, 182, 0.2);
}

.music-detail-container.search-context .back-button a:hover {
  background: linear-gradient(
    135deg,
    rgba(155, 89, 182, 0.2),
    rgba(142, 68, 173, 0.2)
  );
  border-color: rgba(155, 89, 182, 0.4);
}

/* Navigation styling fixes for music detail page */
.music-detail-page header .song_side nav ul li {
  color: var(--text-muted);
  position: relative;
  list-style-type: none;
  font-size: 13px;
  margin-right: 25px;
  cursor: pointer;
  transition: var(--transition);
  padding: 5px 0;
}

.music-detail-page header .song_side nav ul li a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition);
}

.music-detail-page header .song_side nav ul li:hover,
.music-detail-page header .song_side nav ul li a:hover {
  color: var(--text-color);
}

.music-detail-page header .song_side nav ul li.active {
  color: var(--text-color);
}

.music-detail-page header .song_side nav ul li.active span {
  position: absolute;
  width: 100%;
  height: 2.5px;
  background: var(--primary-color);
  bottom: -5px;
  left: 0;
  border-radius: 20px;
}

/* Sidebar navigation styling for music detail page */
.music-detail-page header .menu_side .playlist h4 {
  font-size: 14px;
  font-weight: 400;
  padding: 10px;
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--transition);
  border-radius: var(--border-radius-small);
  position: relative;
}

.music-detail-page header .menu_side .playlist h4 a {
  color: inherit;
  text-decoration: none;
  display: flex;
  align-items: center;
  width: 100%;
  transition: var(--transition);
}

.music-detail-page header .menu_side .playlist h4:hover {
  background-color: var(--hover-light);
  color: var(--text-color);
}

.music-detail-page header .menu_side .playlist h4.active {
  background-color: var(--active-color);
  color: var(--text-color);
}

.music-detail-page header .menu_side .playlist h4.active span {
  position: absolute;
  width: 3px;
  height: 20px;
  background: var(--primary-color);
  left: 0;
  border-radius: 0 2px 2px 0;
}

/* Context-specific navigation highlighting */
.music-detail-container.library-context #nav-library,
.music-detail-container.library-context #sidebar-library {
  color: var(--text-color);
}

.music-detail-container.library-context #nav-library.active span {
  background: #e74c3c;
}

.music-detail-container.library-context #sidebar-library.active span {
  background: #e74c3c;
}

.music-detail-container.recently-played-context #nav-recently-played,
.music-detail-container.recently-played-context #sidebar-recently-played {
  color: var(--text-color);
}

.music-detail-container.recently-played-context
  #nav-recently-played.active
  span {
  background: #f39c12;
}

.music-detail-container.recently-played-context
  #sidebar-recently-played.active
  span {
  background: #f39c12;
}

.music-detail-container.discovery-context #nav-discovery,
.music-detail-container.discovery-context #sidebar-discovery {
  color: var(--text-color);
}

.music-detail-container.discovery-context #nav-discovery.active span {
  background: var(--primary-color);
}

.music-detail-container.discovery-context #sidebar-discovery.active span {
  background: var(--primary-color);
}

.music-detail-container.search-context #nav-discovery,
.music-detail-container.search-context #sidebar-discovery {
  color: var(--text-color);
}

.music-detail-container.search-context #nav-discovery.active span {
  background: #9b59b6;
}

.music-detail-container.search-context #sidebar-discovery.active span {
  background: #9b59b6;
}

/* Fix for navigation conflicts from other pages */
body.music-detail-page header .song_side nav ul li:nth-child(2),
body.music-detail-page header .song_side nav ul li:nth-child(3) {
  color: var(--text-muted) !important;
}

body.music-detail-page header .song_side nav ul li.active {
  color: var(--text-color) !important;
}

/* Additional specificity for navigation items */
body.music-detail-page header .song_side nav ul li#nav-discovery,
body.music-detail-page header .song_side nav ul li#nav-library,
body.music-detail-page header .song_side nav ul li#nav-recently-played {
  color: var(--text-muted);
}

body.music-detail-page header .song_side nav ul li#nav-discovery.active,
body.music-detail-page header .song_side nav ul li#nav-library.active,
body.music-detail-page header .song_side nav ul li#nav-recently-played.active {
  color: var(--text-color) !important;
}

/* Sidebar navigation specificity */
body.music-detail-page header .menu_side .playlist h4#sidebar-discovery,
body.music-detail-page header .menu_side .playlist h4#sidebar-library,
body.music-detail-page header .menu_side .playlist h4#sidebar-recently-played {
  color: var(--text-muted);
}

body.music-detail-page header .menu_side .playlist h4#sidebar-discovery.active,
body.music-detail-page header .menu_side .playlist h4#sidebar-library.active,
body.music-detail-page
  header
  .menu_side
  .playlist
  h4#sidebar-recently-played.active {
  color: var(--text-color) !important;
  background-color: var(--active-color);
}

/* Ensure proper z-index and positioning */
.music-detail-container {
  position: relative;
  z-index: 1;
}

/* Fix for any layout shifts */
.music-detail-content {
  min-height: 300px;
}

/* Smooth transitions for context changes */
.music-detail-container,
.breadcrumb-source,
.back-button a {
  transition: var(--transition);
}

/* Animation for page load */
.music-detail-container {
  animation: fadeIn 0.5s ease-in-out;
}

.music-detail-content {
  animation: slideUp 0.6s ease-in-out;
}

.related-songs {
  animation: slideUp 0.8s ease-in-out;
}

/* Ensure proper loading state */
.music-detail-container.loading {
  opacity: 0.7;
}

.music-detail-container.loaded {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

/* Heart icon animation */
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.heart-animation {
  animation: heartBeat 0.6s ease-in-out;
}

/* Hover effects for better UX */
.related-song-item {
  transition: var(--transition);
}

.related-song-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-light);
}

/* Ensure consistent button styling */
.music-detail-page .action-btn {
  background-color: var(--secondary-color);
  border: none;
  color: var(--text-color);
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  text-decoration: none;
  min-height: 40px;
  white-space: nowrap;
}

.music-detail-page .action-btn:hover {
  background-color: var(--secondary-hover);
  transform: translateY(-2px);
}

.music-detail-page .action-btn i {
  margin-right: 8px;
  font-size: 16px;
}

/* Heart button specific styling */
.music-detail-page .heart-btn.in-library {
  background-color: var(--primary-color);
  color: var(--background-color);
}

.music-detail-page .heart-btn.in-library:hover {
  background-color: var(--primary-hover);
}

/* Responsive breadcrumb */
@media (max-width: 768px) {
  .breadcrumb-navigation {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .breadcrumb {
    font-size: 12px;
  }

  .music-detail-header h2 {
    font-size: 20px;
  }

  .music-info h1 {
    font-size: 24px;
  }

  .music-info h3 {
    font-size: 16px;
  }
}

/* Fix for responsive design issues */
@media (max-width: 992px) {
  .music-detail-content {
    gap: 20px;
  }

  .music-cover {
    min-width: 250px;
    height: 250px;
  }
}

@media (max-width: 576px) {
  .music-cover {
    min-width: 200px;
    height: 200px;
  }

  .music-info h1 {
    font-size: 20px;
  }
}
